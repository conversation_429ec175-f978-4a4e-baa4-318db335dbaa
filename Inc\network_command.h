/**
  ******************************************************************************
  * @file    network_command.h
  * @brief   网络下发指令解析和处理模块
  ******************************************************************************
  * @attention
  *
  * 支持的指令格式：
  * ZL+D<数值> - 设备休眠天数设置
  * ZL+H<数值> - 设备休眠小时数设置
  * ZL+M<数值> - 设备休眠分钟数设置
  * ZL+S<数值> - 设备休眠秒数设置
  * ZL+F<开始时间>E<结束时间> - 工作时间段设置，如ZL+F16E22表示16点到22点工作
  * ZL+N<数值> - 打包N条数据一起发送（暂时不用）
  * ZL+T - 发送设备信息（暂时不用）
  *
  ******************************************************************************
  */

#ifndef __NETWORK_COMMAND_H
#define __NETWORK_COMMAND_H

#ifdef __cplusplus
 extern "C" {
#endif

#include "main.h"

// 指令类型定义
typedef enum {
    CMD_TYPE_UNKNOWN = 0,
    CMD_TYPE_SLEEP_DAYS,      // D - 休眠天数
    CMD_TYPE_SLEEP_HOURS,     // H - 休眠小时数
    CMD_TYPE_SLEEP_MINUTES,   // M - 休眠分钟数
    CMD_TYPE_SLEEP_SECONDS,   // S - 休眠秒数
    CMD_TYPE_WORK_TIME,       // F...E - 工作时间段
    CMD_TYPE_PACK_COUNT,      // N - 打包数据条数（暂时不用）
    CMD_TYPE_SEND_INFO        // T - 发送设备信息（暂时不用）
} NetworkCommand_Type_t;

// 指令解析结果结构体
typedef struct {
    NetworkCommand_Type_t type;
    uint32_t value1;          // 主要数值（休眠时间或开始时间）
    uint32_t value2;          // 次要数值（结束时间，仅用于工作时间段）
    uint8_t is_valid;         // 指令是否有效
} NetworkCommand_Result_t;

// FLASH存储索引定义
#define FLASH_INDEX_SLEEP_TIME      0   // 休眠时间设置（覆盖机制）
#define FLASH_INDEX_WORK_TIME_START 1   // 工作开始时间
#define FLASH_INDEX_WORK_TIME_END   2   // 工作结束时间
#define FLASH_INDEX_PACK_COUNT      3   // 打包数据条数（暂时不用）

// 函数声明
HAL_StatusTypeDef NetworkCommand_Parse(const char* command, NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_SaveToFlash(const NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_LoadFromFlash(NetworkCommand_Type_t type, NetworkCommand_Result_t* result);
HAL_StatusTypeDef NetworkCommand_Process(const char* command);
HAL_StatusTypeDef NetworkCommand_ProcessMultiple(const char* buffer);
uint8_t NetworkCommand_CheckWorkTime(void);
HAL_StatusTypeDef NetworkCommand_UpdateSleepTime(NetworkCommand_Type_t type, uint32_t value);
void NetworkCommand_LoadSettingsOnBoot(void);
void NetworkCommand_PrintResult(const NetworkCommand_Result_t* result);

#ifdef __cplusplus
}
#endif

#endif /* __NETWORK_COMMAND_H */
